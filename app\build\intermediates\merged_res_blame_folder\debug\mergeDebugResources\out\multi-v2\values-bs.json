{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-2:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\edd13c4037c8d7e9a96386414d8d09d2\\transformed\\core-1.13.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,403,507,611,713,8242", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "198,300,398,502,606,708,825,8338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16c514905f5f367dbcd0ea9ecb1cb006\\transformed\\material3-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,415,535,634,732,847,992,1112,1250,1335,1435,1528,1626,1743,1870,1975,2110,2244,2385,2555,2690,2813,2940,3068,3162,3260,3381,3509,3606,3709,3818,3957,4102,4211,4311,4396,4489,4584,4678,4769,4878,4966,5049,5146,5250,5343,5440,5528,5636,5733,5835,5973,6063,6171", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "169,289,410,530,629,727,842,987,1107,1245,1330,1430,1523,1621,1738,1865,1970,2105,2239,2380,2550,2685,2808,2935,3063,3157,3255,3376,3504,3601,3704,3813,3952,4097,4206,4306,4391,4484,4579,4673,4764,4873,4961,5044,5141,5245,5338,5435,5523,5631,5728,5830,5968,6058,6166,6265"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1467,1586,1706,1827,1947,2046,2144,2259,2404,2524,2662,2747,2847,2940,3038,3155,3282,3387,3522,3656,3797,3967,4102,4225,4352,4480,4574,4672,4793,4921,5018,5121,5230,5369,5514,5623,5723,5808,5901,5996,6090,6181,6290,6378,6461,6558,6662,6755,6852,6940,7048,7145,7247,7385,7475,7583", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "1581,1701,1822,1942,2041,2139,2254,2399,2519,2657,2742,2842,2935,3033,3150,3277,3382,3517,3651,3792,3962,4097,4220,4347,4475,4569,4667,4788,4916,5013,5116,5225,5364,5509,5618,5718,5803,5896,5991,6085,6176,6285,6373,6456,6553,6657,6750,6847,6935,7043,7140,7242,7380,7470,7578,7677"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a8c37811fe2277121ba6925381f444a4\\transformed\\foundation-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,91", "endOffsets": "140,232"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8611,8701", "endColumns": "89,91", "endOffsets": "8696,8788"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b627dcadf419b9cfc3bc664cd2dabec0\\transformed\\ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,206,294,388,487,573,650,742,834,919,991,1062,1143,1229,1302,1382,1452", "endColumns": "100,87,93,98,85,76,91,91,84,71,70,80,85,72,79,69,117", "endOffsets": "201,289,383,482,568,645,737,829,914,986,1057,1138,1224,1297,1377,1447,1565"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "830,931,1019,1113,1212,1298,1375,7682,7774,7859,7931,8002,8083,8169,8343,8423,8493", "endColumns": "100,87,93,98,85,76,91,91,84,71,70,80,85,72,79,69,117", "endOffsets": "926,1014,1108,1207,1293,1370,1462,7769,7854,7926,7997,8078,8164,8237,8418,8488,8606"}}]}]}