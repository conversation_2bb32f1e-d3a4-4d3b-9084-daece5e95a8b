-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-33:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\16c514905f5f367dbcd0ea9ecb1cb006\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\fb9b55240760d70ab8faaf36672b6f5d\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\09bc2df0ef1a65e73ff62ae629a248c7\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f9839441bdc731beb97eec12ae19e34b\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\a8c37811fe2277121ba6925381f444a4\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2643ffab4dea9d61e428bd574bbc5b85\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\fec97ee586b3bb40dfcdcb8c13a68468\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\8f3b2ca819ace6fe49b0118063533dfb\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2a2188ce9c68f18b32c79346ad69a474\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\c35a2fd4fdf57000bd157bf4a3133985\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\792812c09bee546821a2502c4433f90c\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\54bbabbcc366926a7ba602b24e997873\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\fff0cd91c07110063999ce1715424aa2\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ca64575ba17c53c0f08582d9f9f0d2db\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f778fb6f5e450aab7e1b8cf3df7ffdbc\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\b627dcadf419b9cfc3bc664cd2dabec0\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0c1a05c20bb6175a0a7196b5e22498b0\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ee1e78952eb129ae8b7ed8bfe6363049\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\67bb318cb22e9f5f16ea33a33e75adb6\transformed\activity-compose-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\4eee461916d35ec08fedfbcf90df18fc\transformed\activity-ktx-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\8f44f9db4955152d1ac9b37cfc695214\transformed\activity-1.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a035878390b2ffa4dce5202e578d0787\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\1c49528fe086e1295533ab86cad44cee\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9eca0d43da74e1220bfd595676f73fae\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3fff80e0f8323ecf7677e55f413abb4\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bcc4eaa997cce8e72514bea29a825508\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\06b8ddd399c83ac244d13456d4b9d104\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\ad25fc0ad3d8f25566d99b4f5adef574\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\c79fb4a466969789f872a920d2b10315\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\8acc4fed87a2e4c2fe6cddae1648b1d4\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a183fc54f2cd874be4dde138a21b42e9\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\03854f00f95ffd51fb05267d495ce140\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\f78714015903cedef57e7f147e6a80e1\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b77e27445fd35d1a4d5c76c2bb4bc409\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\bf1be76432d35f9cb1672b141575c06e\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\a441c510314723cfbe2f3f10af806edb\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b28e1410de531e7144052e35b754ff7f\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45f2dcadd9f437b2a0ea3c179cb30f20\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\b19b70b7432c9d4490d484ab8c8322a4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\72ff0d40dd6c2dad09bb372ab543e766\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9489b70cff5edb81f58034bb78426aec\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\89e2731bb1d204ea02d4b5aa0f9ae55a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:5:5-31:19
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:5:5-31:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0c1a05c20bb6175a0a7196b5e22498b0\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0c1a05c20bb6175a0a7196b5e22498b0\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ee1e78952eb129ae8b7ed8bfe6363049\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ee1e78952eb129ae8b7ed8bfe6363049\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3fff80e0f8323ecf7677e55f413abb4\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3fff80e0f8323ecf7677e55f413abb4\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\b19b70b7432c9d4490d484ab8c8322a4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\b19b70b7432c9d4490d484ab8c8322a4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\72ff0d40dd6c2dad09bb372ab543e766\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\72ff0d40dd6c2dad09bb372ab543e766\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:12:9-35
	android:label
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:10:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:8:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:11:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:14:9-29
	android:icon
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:9:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:13:9-47
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:7:9-65
activity#com.dev.aa103_poc.MainActivity
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:15:9-25:20
	android:label
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:18:13-45
	android:exported
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:17:13-36
	android:theme
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:19:13-51
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:16:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:20:13-24:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:17-77
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:27-74
activity#com.dev.aa103_poc.ui.signin.SignInActivity
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:26:9-30:54
	android:label
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:29:13-36
	android:exported
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:28:13-37
	android:theme
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:30:13-51
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:27:13-53
uses-sdk
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\16c514905f5f367dbcd0ea9ecb1cb006\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\16c514905f5f367dbcd0ea9ecb1cb006\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\fb9b55240760d70ab8faaf36672b6f5d\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\fb9b55240760d70ab8faaf36672b6f5d\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\09bc2df0ef1a65e73ff62ae629a248c7\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\09bc2df0ef1a65e73ff62ae629a248c7\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f9839441bdc731beb97eec12ae19e34b\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f9839441bdc731beb97eec12ae19e34b\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\a8c37811fe2277121ba6925381f444a4\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\a8c37811fe2277121ba6925381f444a4\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2643ffab4dea9d61e428bd574bbc5b85\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2643ffab4dea9d61e428bd574bbc5b85\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\fec97ee586b3bb40dfcdcb8c13a68468\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\fec97ee586b3bb40dfcdcb8c13a68468\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\8f3b2ca819ace6fe49b0118063533dfb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\8f3b2ca819ace6fe49b0118063533dfb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2a2188ce9c68f18b32c79346ad69a474\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\2a2188ce9c68f18b32c79346ad69a474\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\c35a2fd4fdf57000bd157bf4a3133985\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\c35a2fd4fdf57000bd157bf4a3133985\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\792812c09bee546821a2502c4433f90c\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\792812c09bee546821a2502c4433f90c\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\54bbabbcc366926a7ba602b24e997873\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\54bbabbcc366926a7ba602b24e997873\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\fff0cd91c07110063999ce1715424aa2\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\fff0cd91c07110063999ce1715424aa2\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ca64575ba17c53c0f08582d9f9f0d2db\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ca64575ba17c53c0f08582d9f9f0d2db\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f778fb6f5e450aab7e1b8cf3df7ffdbc\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f778fb6f5e450aab7e1b8cf3df7ffdbc\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\b627dcadf419b9cfc3bc664cd2dabec0\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\b627dcadf419b9cfc3bc664cd2dabec0\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0c1a05c20bb6175a0a7196b5e22498b0\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0c1a05c20bb6175a0a7196b5e22498b0\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ee1e78952eb129ae8b7ed8bfe6363049\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ee1e78952eb129ae8b7ed8bfe6363049\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\67bb318cb22e9f5f16ea33a33e75adb6\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\67bb318cb22e9f5f16ea33a33e75adb6\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\4eee461916d35ec08fedfbcf90df18fc\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\4eee461916d35ec08fedfbcf90df18fc\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\8f44f9db4955152d1ac9b37cfc695214\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\8f44f9db4955152d1ac9b37cfc695214\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a035878390b2ffa4dce5202e578d0787\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a035878390b2ffa4dce5202e578d0787\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\1c49528fe086e1295533ab86cad44cee\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\1c49528fe086e1295533ab86cad44cee\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9eca0d43da74e1220bfd595676f73fae\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9eca0d43da74e1220bfd595676f73fae\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3fff80e0f8323ecf7677e55f413abb4\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3fff80e0f8323ecf7677e55f413abb4\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bcc4eaa997cce8e72514bea29a825508\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bcc4eaa997cce8e72514bea29a825508\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\06b8ddd399c83ac244d13456d4b9d104\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\06b8ddd399c83ac244d13456d4b9d104\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\ad25fc0ad3d8f25566d99b4f5adef574\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\ad25fc0ad3d8f25566d99b4f5adef574\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\c79fb4a466969789f872a920d2b10315\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\c79fb4a466969789f872a920d2b10315\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\8acc4fed87a2e4c2fe6cddae1648b1d4\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\8acc4fed87a2e4c2fe6cddae1648b1d4\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a183fc54f2cd874be4dde138a21b42e9\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a183fc54f2cd874be4dde138a21b42e9\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\03854f00f95ffd51fb05267d495ce140\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\03854f00f95ffd51fb05267d495ce140\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\f78714015903cedef57e7f147e6a80e1\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\f78714015903cedef57e7f147e6a80e1\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b77e27445fd35d1a4d5c76c2bb4bc409\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b77e27445fd35d1a4d5c76c2bb4bc409\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\bf1be76432d35f9cb1672b141575c06e\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\bf1be76432d35f9cb1672b141575c06e\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\a441c510314723cfbe2f3f10af806edb\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\a441c510314723cfbe2f3f10af806edb\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b28e1410de531e7144052e35b754ff7f\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b28e1410de531e7144052e35b754ff7f\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45f2dcadd9f437b2a0ea3c179cb30f20\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45f2dcadd9f437b2a0ea3c179cb30f20\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\b19b70b7432c9d4490d484ab8c8322a4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\b19b70b7432c9d4490d484ab8c8322a4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\72ff0d40dd6c2dad09bb372ab543e766\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\72ff0d40dd6c2dad09bb372ab543e766\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9489b70cff5edb81f58034bb78426aec\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9489b70cff5edb81f58034bb78426aec\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\89e2731bb1d204ea02d4b5aa0f9ae55a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\89e2731bb1d204ea02d4b5aa0f9ae55a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0c1a05c20bb6175a0a7196b5e22498b0\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0c1a05c20bb6175a0a7196b5e22498b0\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0c1a05c20bb6175a0a7196b5e22498b0\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ee1e78952eb129ae8b7ed8bfe6363049\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ee1e78952eb129ae8b7ed8bfe6363049\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ee1e78952eb129ae8b7ed8bfe6363049\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3fff80e0f8323ecf7677e55f413abb4\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3fff80e0f8323ecf7677e55f413abb4\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\72ff0d40dd6c2dad09bb372ab543e766\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\72ff0d40dd6c2dad09bb372ab543e766\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3fff80e0f8323ecf7677e55f413abb4\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3fff80e0f8323ecf7677e55f413abb4\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3fff80e0f8323ecf7677e55f413abb4\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
