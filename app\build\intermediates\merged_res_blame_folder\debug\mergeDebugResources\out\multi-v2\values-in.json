{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-2:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\eacb11c3dc0856eb37fac4a2b512c744\\transformed\\ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,977,1044,1126,1209,1281,1359,1425", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,972,1039,1121,1204,1276,1354,1420,1539"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "835,925,1007,1105,1205,1291,1374,7632,7719,7804,7874,7941,8023,8106,8279,8357,8423", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "920,1002,1100,1200,1286,1369,1460,7714,7799,7869,7936,8018,8101,8173,8352,8418,8537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6c315b7243f9366af29f4dfe1c8f7ae0\\transformed\\foundation-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,89", "endOffsets": "136,226"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8542,8628", "endColumns": "85,89", "endOffsets": "8623,8713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7dcdec81c6dab61c12a6af226d4a0a33\\transformed\\material3-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,401,517,615,721,844,991,1114,1264,1351,1455,1548,1652,1770,1890,1999,2139,2277,2406,2584,2706,2826,2949,3072,3166,3267,3387,3520,3622,3729,3836,3978,4125,4234,4334,4410,4506,4601,4690,4775,4874,4954,5037,5136,5235,5332,5432,5519,5622,5721,5825,5942,6022,6127", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "169,284,396,512,610,716,839,986,1109,1259,1346,1450,1543,1647,1765,1885,1994,2134,2272,2401,2579,2701,2821,2944,3067,3161,3262,3382,3515,3617,3724,3831,3973,4120,4229,4329,4405,4501,4596,4685,4770,4869,4949,5032,5131,5230,5327,5427,5514,5617,5716,5820,5937,6017,6122,6217"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1465,1584,1699,1811,1927,2025,2131,2254,2401,2524,2674,2761,2865,2958,3062,3180,3300,3409,3549,3687,3816,3994,4116,4236,4359,4482,4576,4677,4797,4930,5032,5139,5246,5388,5535,5644,5744,5820,5916,6011,6100,6185,6284,6364,6447,6546,6645,6742,6842,6929,7032,7131,7235,7352,7432,7537", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "1579,1694,1806,1922,2020,2126,2249,2396,2519,2669,2756,2860,2953,3057,3175,3295,3404,3544,3682,3811,3989,4111,4231,4354,4477,4571,4672,4792,4925,5027,5134,5241,5383,5530,5639,5739,5815,5911,6006,6095,6180,6279,6359,6442,6541,6640,6737,6837,6924,7027,7126,7230,7347,7427,7532,7627"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f09be75b05f28281f1ac42504082277d\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,399,496,602,720,8178", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "195,297,394,491,597,715,830,8274"}}]}]}