1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dev.aa103_poc"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="25"
9        android:targetSdkVersion="34" />
10
11    <permission
11-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:5:5-31:19
18        android:allowBackup="true"
18-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\edd13c4037c8d7e9a96386414d8d09d2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.AA103_POC" >
29-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:13:9-47
30        <activity
30-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:15:9-25:20
31            android:name="com.dev.aa103_poc.MainActivity"
31-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:16:13-41
32            android:exported="true"
32-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:17:13-36
33            android:label="@string/app_name"
33-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:18:13-45
34            android:theme="@style/Theme.AA103_POC" >
34-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:19:13-51
35            <intent-filter>
35-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:20:13-24:29
36                <action android:name="android.intent.action.MAIN" />
36-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:17-69
36-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:25-66
37
38                <category android:name="android.intent.category.LAUNCHER" />
38-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:17-77
38-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:27-74
39            </intent-filter>
40        </activity>
41        <activity
41-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:26:9-30:54
42            android:name="com.dev.aa103_poc.ui.signin.SignInActivity"
42-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:27:13-53
43            android:exported="false"
43-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:28:13-37
44            android:label="Sign In"
44-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:29:13-36
45            android:theme="@style/Theme.AA103_POC" />
45-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:30:13-51
46        <activity
46-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0c1a05c20bb6175a0a7196b5e22498b0\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
47            android:name="androidx.activity.ComponentActivity"
47-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0c1a05c20bb6175a0a7196b5e22498b0\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
48            android:exported="true" />
48-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0c1a05c20bb6175a0a7196b5e22498b0\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
49        <activity
49-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ee1e78952eb129ae8b7ed8bfe6363049\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
50            android:name="androidx.compose.ui.tooling.PreviewActivity"
50-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ee1e78952eb129ae8b7ed8bfe6363049\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
51            android:exported="true" />
51-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\ee1e78952eb129ae8b7ed8bfe6363049\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
52
53        <provider
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
54            android:name="androidx.startup.InitializationProvider"
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
55            android:authorities="com.dev.aa103_poc.androidx-startup"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
56            android:exported="false" >
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\43918a46e6e8556214d8389482f82ae6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3fff80e0f8323ecf7677e55f413abb4\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3fff80e0f8323ecf7677e55f413abb4\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\a3fff80e0f8323ecf7677e55f413abb4\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
65                android:value="androidx.startup" />
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
66        </provider>
67
68        <receiver
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
69            android:name="androidx.profileinstaller.ProfileInstallReceiver"
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
70            android:directBootAware="false"
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
71            android:enabled="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
72            android:exported="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
73            android:permission="android.permission.DUMP" >
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
75                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
78                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
81                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
84                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\01cf6995cee971590471c30bf0a24415\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
85            </intent-filter>
86        </receiver>
87    </application>
88
89</manifest>
