-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-33:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7dcdec81c6dab61c12a6af226d4a0a33\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\76129d56b11673cdc163b1f854863bb9\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\eab26c544bad000b383cbc1ed688a028\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\731bd3a64296d36975020176c3209c25\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\850aa6faadacb36c2f31a85bb24d62d0\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\6c315b7243f9366af29f4dfe1c8f7ae0\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\47ad33e0656c07bf38cdd1ca829470d5\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\82bd380effb03a4c12da3b7f68e15e80\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\5d90dbf5d20b6e45aff7fb23172b96df\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\d00a22210247b429645a3bd0a9f33756\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\1d9bf9b9ea8eaebe50b32b2a9bb3c46b\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\571041d3c9a421d0f63d117581301c7d\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\9cb3adf58e8303ad89d036d5c6bab036\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\5ede3dd4422d0c8887cbce642cc65f79\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\77190579b14692cc03e2fd77558f8c13\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\79de50afc33789eaa7084c5ef8675a7b\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\eacb11c3dc0856eb37fac4a2b512c744\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3300b465058c171afcf580ff5257b7b9\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f166587d87c57013c216a985d1ffd905\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\3a1489acdf2f6a009f498af46b07135a\transformed\activity-compose-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\4d450e59c5cbe9779ac02fe62386aa5c\transformed\activity-ktx-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f1dd9683d3252354f25fb0e7f966444\transformed\activity-1.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a35f7d31cadafd367b06538ff300509d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\b9e3b9e155d63214c49431f5825f3078\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a52e44f058acfa18219f95b3654dc3a\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\83b6fe2f113fd6dc52d10c2669ed5b47\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\e7d8bf2bf5930e0bcc355e462a0486b2\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\8574fafa6865221b8408dff618ba5fbb\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf04b2d1d40108b7ea207ab9d1b1877e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\cc17ce26dc6b7b9f7b7595328dfe575b\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\669690d911cbd688f0e32512f9435510\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\c332aa400e2daed57f4ed6cdd11b8154\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\103f47c86290f99baea4887936ee285d\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\46ec1ba3ad0a9a13d6a8cfbccefb45de\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\0d9b88cb658caa407ccc7fe5573fac6c\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\24ea3cbb311a2f49b4036389981ddffd\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\080edbe7037bb3e12a306a9ad9a2e750\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\4e3d0fc5cabb9df4e40b1f780e53d9e6\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b004e4bbb651db40db9466ffb1d3325\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d570faab30099b51eebdbe93cec168b0\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\696823954e4e1e8d843623fd5726324c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7fefc9a34ec0bb3cbf781be7c29a3597\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\af9c8537511044e31193015bf7a3f44b\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e55f7958ccb7d44e8c90e791d90ea1dd\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:5:5-31:19
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:5:5-31:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3300b465058c171afcf580ff5257b7b9\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3300b465058c171afcf580ff5257b7b9\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f166587d87c57013c216a985d1ffd905\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f166587d87c57013c216a985d1ffd905\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\83b6fe2f113fd6dc52d10c2669ed5b47\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\83b6fe2f113fd6dc52d10c2669ed5b47\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\e7d8bf2bf5930e0bcc355e462a0486b2\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\e7d8bf2bf5930e0bcc355e462a0486b2\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\696823954e4e1e8d843623fd5726324c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\696823954e4e1e8d843623fd5726324c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7fefc9a34ec0bb3cbf781be7c29a3597\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7fefc9a34ec0bb3cbf781be7c29a3597\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:12:9-35
	android:label
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:10:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:8:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:11:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:14:9-29
	android:icon
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:9:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:13:9-47
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:7:9-65
activity#com.dev.aa103_poc.MainActivity
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:15:9-25:20
	android:label
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:18:13-45
	android:exported
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:17:13-36
	android:theme
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:19:13-51
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:16:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:20:13-24:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:17-77
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:27-74
activity#com.dev.aa103_poc.ui.signin.SignInActivity
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:26:9-30:54
	android:label
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:29:13-36
	android:exported
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:28:13-37
	android:theme
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:30:13-51
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:27:13-53
uses-sdk
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7dcdec81c6dab61c12a6af226d4a0a33\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\7dcdec81c6dab61c12a6af226d4a0a33\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\76129d56b11673cdc163b1f854863bb9\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\76129d56b11673cdc163b1f854863bb9\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\eab26c544bad000b383cbc1ed688a028\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\eab26c544bad000b383cbc1ed688a028\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\731bd3a64296d36975020176c3209c25\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\731bd3a64296d36975020176c3209c25\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\850aa6faadacb36c2f31a85bb24d62d0\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\850aa6faadacb36c2f31a85bb24d62d0\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\6c315b7243f9366af29f4dfe1c8f7ae0\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\6c315b7243f9366af29f4dfe1c8f7ae0\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\47ad33e0656c07bf38cdd1ca829470d5\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\47ad33e0656c07bf38cdd1ca829470d5\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\82bd380effb03a4c12da3b7f68e15e80\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\82bd380effb03a4c12da3b7f68e15e80\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\5d90dbf5d20b6e45aff7fb23172b96df\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\5d90dbf5d20b6e45aff7fb23172b96df\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\d00a22210247b429645a3bd0a9f33756\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\d00a22210247b429645a3bd0a9f33756\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\1d9bf9b9ea8eaebe50b32b2a9bb3c46b\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\1d9bf9b9ea8eaebe50b32b2a9bb3c46b\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\571041d3c9a421d0f63d117581301c7d\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\571041d3c9a421d0f63d117581301c7d\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\9cb3adf58e8303ad89d036d5c6bab036\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\9cb3adf58e8303ad89d036d5c6bab036\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\5ede3dd4422d0c8887cbce642cc65f79\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\5ede3dd4422d0c8887cbce642cc65f79\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\77190579b14692cc03e2fd77558f8c13\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\77190579b14692cc03e2fd77558f8c13\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\79de50afc33789eaa7084c5ef8675a7b\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\79de50afc33789eaa7084c5ef8675a7b\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\eacb11c3dc0856eb37fac4a2b512c744\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\eacb11c3dc0856eb37fac4a2b512c744\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3300b465058c171afcf580ff5257b7b9\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3300b465058c171afcf580ff5257b7b9\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f166587d87c57013c216a985d1ffd905\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f166587d87c57013c216a985d1ffd905\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\3a1489acdf2f6a009f498af46b07135a\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\3a1489acdf2f6a009f498af46b07135a\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\4d450e59c5cbe9779ac02fe62386aa5c\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\4d450e59c5cbe9779ac02fe62386aa5c\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f1dd9683d3252354f25fb0e7f966444\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f1dd9683d3252354f25fb0e7f966444\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a35f7d31cadafd367b06538ff300509d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a35f7d31cadafd367b06538ff300509d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\b9e3b9e155d63214c49431f5825f3078\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\b9e3b9e155d63214c49431f5825f3078\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a52e44f058acfa18219f95b3654dc3a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a52e44f058acfa18219f95b3654dc3a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\83b6fe2f113fd6dc52d10c2669ed5b47\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\83b6fe2f113fd6dc52d10c2669ed5b47\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\e7d8bf2bf5930e0bcc355e462a0486b2\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\e7d8bf2bf5930e0bcc355e462a0486b2\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\8574fafa6865221b8408dff618ba5fbb\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\8574fafa6865221b8408dff618ba5fbb\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf04b2d1d40108b7ea207ab9d1b1877e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\cf04b2d1d40108b7ea207ab9d1b1877e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\cc17ce26dc6b7b9f7b7595328dfe575b\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\cc17ce26dc6b7b9f7b7595328dfe575b\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\669690d911cbd688f0e32512f9435510\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\669690d911cbd688f0e32512f9435510\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\c332aa400e2daed57f4ed6cdd11b8154\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\c332aa400e2daed57f4ed6cdd11b8154\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\103f47c86290f99baea4887936ee285d\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\103f47c86290f99baea4887936ee285d\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\46ec1ba3ad0a9a13d6a8cfbccefb45de\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\46ec1ba3ad0a9a13d6a8cfbccefb45de\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\0d9b88cb658caa407ccc7fe5573fac6c\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\0d9b88cb658caa407ccc7fe5573fac6c\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\24ea3cbb311a2f49b4036389981ddffd\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\24ea3cbb311a2f49b4036389981ddffd\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\080edbe7037bb3e12a306a9ad9a2e750\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\080edbe7037bb3e12a306a9ad9a2e750\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\4e3d0fc5cabb9df4e40b1f780e53d9e6\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\4e3d0fc5cabb9df4e40b1f780e53d9e6\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b004e4bbb651db40db9466ffb1d3325\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b004e4bbb651db40db9466ffb1d3325\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d570faab30099b51eebdbe93cec168b0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d570faab30099b51eebdbe93cec168b0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\696823954e4e1e8d843623fd5726324c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\696823954e4e1e8d843623fd5726324c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7fefc9a34ec0bb3cbf781be7c29a3597\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7fefc9a34ec0bb3cbf781be7c29a3597\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\af9c8537511044e31193015bf7a3f44b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\af9c8537511044e31193015bf7a3f44b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e55f7958ccb7d44e8c90e791d90ea1dd\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e55f7958ccb7d44e8c90e791d90ea1dd\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3300b465058c171afcf580ff5257b7b9\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3300b465058c171afcf580ff5257b7b9\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3300b465058c171afcf580ff5257b7b9\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f166587d87c57013c216a985d1ffd905\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f166587d87c57013c216a985d1ffd905\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\f166587d87c57013c216a985d1ffd905\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\83b6fe2f113fd6dc52d10c2669ed5b47\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\e7d8bf2bf5930e0bcc355e462a0486b2\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\e7d8bf2bf5930e0bcc355e462a0486b2\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7fefc9a34ec0bb3cbf781be7c29a3597\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7fefc9a34ec0bb3cbf781be7c29a3597\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\83b6fe2f113fd6dc52d10c2669ed5b47\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\83b6fe2f113fd6dc52d10c2669ed5b47\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\83b6fe2f113fd6dc52d10c2669ed5b47\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\83b6fe2f113fd6dc52d10c2669ed5b47\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\83b6fe2f113fd6dc52d10c2669ed5b47\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\83b6fe2f113fd6dc52d10c2669ed5b47\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\83b6fe2f113fd6dc52d10c2669ed5b47\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\f09be75b05f28281f1ac42504082277d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\e7d8bf2bf5930e0bcc355e462a0486b2\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\e7d8bf2bf5930e0bcc355e462a0486b2\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\e7d8bf2bf5930e0bcc355e462a0486b2\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\24e1cea6d5041da26cdad1d0c829117c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
