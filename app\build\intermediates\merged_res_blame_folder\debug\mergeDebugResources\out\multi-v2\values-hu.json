{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-2:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7dcdec81c6dab61c12a6af226d4a0a33\\transformed\\material3-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,292,400,516,611,708,822,962,1085,1232,1317,1417,1515,1617,1739,1876,1981,2121,2259,2385,2581,2704,2826,2948,3074,3173,3268,3387,3524,3626,3737,3841,3986,4133,4240,4347,4431,4529,4623,4711,4798,4899,4980,5063,5162,5268,5363,5466,5552,5661,5759,5865,5986,6067,6179", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "171,287,395,511,606,703,817,957,1080,1227,1312,1412,1510,1612,1734,1871,1976,2116,2254,2380,2576,2699,2821,2943,3069,3168,3263,3382,3519,3621,3732,3836,3981,4128,4235,4342,4426,4524,4618,4706,4793,4894,4975,5058,5157,5263,5358,5461,5547,5656,5754,5860,5981,6062,6174,6272"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1471,1592,1708,1816,1932,2027,2124,2238,2378,2501,2648,2733,2833,2931,3033,3155,3292,3397,3537,3675,3801,3997,4120,4242,4364,4490,4589,4684,4803,4940,5042,5153,5257,5402,5549,5656,5763,5847,5945,6039,6127,6214,6315,6396,6479,6578,6684,6779,6882,6968,7077,7175,7281,7402,7483,7595", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "1587,1703,1811,1927,2022,2119,2233,2373,2496,2643,2728,2828,2926,3028,3150,3287,3392,3532,3670,3796,3992,4115,4237,4359,4485,4584,4679,4798,4935,5037,5148,5252,5397,5544,5651,5758,5842,5940,6034,6122,6209,6310,6391,6474,6573,6679,6774,6877,6963,7072,7170,7276,7397,7478,7590,7688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\eacb11c3dc0856eb37fac4a2b512c744\\transformed\\ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,989,1053,1136,1224,1298,1377,1443", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,984,1048,1131,1219,1293,1372,1438,1559"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "827,922,1010,1107,1206,1293,1375,7693,7782,7869,7933,7997,8080,8168,8343,8422,8488", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "917,1005,1102,1201,1288,1370,1466,7777,7864,7928,7992,8075,8163,8237,8417,8483,8604"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6c315b7243f9366af29f4dfe1c8f7ae0\\transformed\\foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,96", "endOffsets": "139,236"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8609,8698", "endColumns": "88,96", "endOffsets": "8693,8790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f09be75b05f28281f1ac42504082277d\\transformed\\core-1.13.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,406,507,610,717,8242", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "197,299,401,502,605,712,822,8338"}}]}]}