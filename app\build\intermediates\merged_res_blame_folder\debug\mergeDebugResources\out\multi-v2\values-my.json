{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-2:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16c514905f5f367dbcd0ea9ecb1cb006\\transformed\\material3-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,192,326,437,571,686,786,903,1052,1176,1339,1425,1524,1617,1719,1839,1966,2070,2196,2327,2471,2639,2761,2878,2997,3124,3218,3315,3446,3583,3685,3797,3902,4028,4157,4260,4363,4444,4542,4638,4725,4811,4930,5010,5094,5194,5296,5392,5490,5577,5684,5783,5884,6005,6085,6208", "endColumns": "136,133,110,133,114,99,116,148,123,162,85,98,92,101,119,126,103,125,130,143,167,121,116,118,126,93,96,130,136,101,111,104,125,128,102,102,80,97,95,86,85,118,79,83,99,101,95,97,86,106,98,100,120,79,122,117", "endOffsets": "187,321,432,566,681,781,898,1047,1171,1334,1420,1519,1612,1714,1834,1961,2065,2191,2322,2466,2634,2756,2873,2992,3119,3213,3310,3441,3578,3680,3792,3897,4023,4152,4255,4358,4439,4537,4633,4720,4806,4925,5005,5089,5189,5291,5387,5485,5572,5679,5778,5879,6000,6080,6203,6321"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1503,1640,1774,1885,2019,2134,2234,2351,2500,2624,2787,2873,2972,3065,3167,3287,3414,3518,3644,3775,3919,4087,4209,4326,4445,4572,4666,4763,4894,5031,5133,5245,5350,5476,5605,5708,5811,5892,5990,6086,6173,6259,6378,6458,6542,6642,6744,6840,6938,7025,7132,7231,7332,7453,7533,7656", "endColumns": "136,133,110,133,114,99,116,148,123,162,85,98,92,101,119,126,103,125,130,143,167,121,116,118,126,93,96,130,136,101,111,104,125,128,102,102,80,97,95,86,85,118,79,83,99,101,95,97,86,106,98,100,120,79,122,117", "endOffsets": "1635,1769,1880,2014,2129,2229,2346,2495,2619,2782,2868,2967,3060,3162,3282,3409,3513,3639,3770,3914,4082,4204,4321,4440,4567,4661,4758,4889,5026,5128,5240,5345,5471,5600,5703,5806,5887,5985,6081,6168,6254,6373,6453,6537,6637,6739,6835,6933,7020,7127,7226,7327,7448,7528,7651,7769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a8c37811fe2277121ba6925381f444a4\\transformed\\foundation-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,107", "endOffsets": "159,267"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8700,8809", "endColumns": "108,107", "endOffsets": "8804,8912"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b627dcadf419b9cfc3bc664cd2dabec0\\transformed\\ui-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,287,391,495,578,662,761,850,932,998,1065,1152,1238,1313,1394,1460", "endColumns": "93,87,103,103,82,83,98,88,81,65,66,86,85,74,80,65,125", "endOffsets": "194,282,386,490,573,657,756,845,927,993,1060,1147,1233,1308,1389,1455,1581"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "847,941,1029,1133,1237,1320,1404,7774,7863,7945,8011,8078,8165,8251,8427,8508,8574", "endColumns": "93,87,103,103,82,83,98,88,81,65,66,86,85,74,80,65,125", "endOffsets": "936,1024,1128,1232,1315,1399,1498,7858,7940,8006,8073,8160,8246,8321,8503,8569,8695"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\edd13c4037c8d7e9a96386414d8d09d2\\transformed\\core-1.13.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,208,312,415,517,622,728,8326", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "203,307,410,512,617,723,842,8422"}}]}]}