{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-2:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a8c37811fe2277121ba6925381f444a4\\transformed\\foundation-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,94", "endOffsets": "147,242"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8369,8466", "endColumns": "96,94", "endOffsets": "8461,8556"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\edd13c4037c8d7e9a96386414d8d09d2\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,304,402,500,603,708,8000", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "196,299,397,495,598,703,815,8096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16c514905f5f367dbcd0ea9ecb1cb006\\transformed\\material3-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,280,393,505,604,697,807,937,1061,1202,1288,1388,1479,1577,1695,1811,1916,2043,2167,2295,2447,2570,2688,2812,2933,3025,3124,3236,3369,3465,3583,3690,3816,3950,4060,4158,4239,4333,4427,4513,4596,4701,4781,4868,4967,5069,5163,5267,5353,5454,5552,5655,5772,5852,5962", "endColumns": "112,111,112,111,98,92,109,129,123,140,85,99,90,97,117,115,104,126,123,127,151,122,117,123,120,91,98,111,132,95,117,106,125,133,109,97,80,93,93,85,82,104,79,86,98,101,93,103,85,100,97,102,116,79,109,105", "endOffsets": "163,275,388,500,599,692,802,932,1056,1197,1283,1383,1474,1572,1690,1806,1911,2038,2162,2290,2442,2565,2683,2807,2928,3020,3119,3231,3364,3460,3578,3685,3811,3945,4055,4153,4234,4328,4422,4508,4591,4696,4776,4863,4962,5064,5158,5262,5348,5449,5547,5650,5767,5847,5957,6063"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1439,1552,1664,1777,1889,1988,2081,2191,2321,2445,2586,2672,2772,2863,2961,3079,3195,3300,3427,3551,3679,3831,3954,4072,4196,4317,4409,4508,4620,4753,4849,4967,5074,5200,5334,5444,5542,5623,5717,5811,5897,5980,6085,6165,6252,6351,6453,6547,6651,6737,6838,6936,7039,7156,7236,7346", "endColumns": "112,111,112,111,98,92,109,129,123,140,85,99,90,97,117,115,104,126,123,127,151,122,117,123,120,91,98,111,132,95,117,106,125,133,109,97,80,93,93,85,82,104,79,86,98,101,93,103,85,100,97,102,116,79,109,105", "endOffsets": "1547,1659,1772,1884,1983,2076,2186,2316,2440,2581,2667,2767,2858,2956,3074,3190,3295,3422,3546,3674,3826,3949,4067,4191,4312,4404,4503,4615,4748,4844,4962,5069,5195,5329,5439,5537,5618,5712,5806,5892,5975,6080,6160,6247,6346,6448,6542,6646,6732,6833,6931,7034,7151,7231,7341,7447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b627dcadf419b9cfc3bc664cd2dabec0\\transformed\\ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,958,1024,1110,1199,1272,1350,1417", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,953,1019,1105,1194,1267,1345,1412,1535"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "820,906,983,1080,1181,1269,1354,7452,7538,7621,7686,7752,7838,7927,8101,8179,8246", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "901,978,1075,1176,1264,1349,1434,7533,7616,7681,7747,7833,7922,7995,8174,8241,8364"}}]}]}