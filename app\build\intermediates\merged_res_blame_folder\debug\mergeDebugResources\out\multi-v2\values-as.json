{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-2:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\edd13c4037c8d7e9a96386414d8d09d2\\transformed\\core-1.13.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,206,309,417,522,626,726,8225", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "201,304,412,517,621,721,850,8321"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b627dcadf419b9cfc3bc664cd2dabec0\\transformed\\ui-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,378,476,563,660,759,848,938,1006,1078,1161,1246,1325,1400,1466", "endColumns": "94,84,92,97,86,96,98,88,89,67,71,82,84,78,74,65,117", "endOffsets": "195,280,373,471,558,655,754,843,933,1001,1073,1156,1241,1320,1395,1461,1579"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "855,950,1035,1128,1226,1313,1410,7659,7748,7838,7906,7978,8061,8146,8326,8401,8467", "endColumns": "94,84,92,97,86,96,98,88,89,67,71,82,84,78,74,65,117", "endOffsets": "945,1030,1123,1221,1308,1405,1504,7743,7833,7901,7973,8056,8141,8220,8396,8462,8580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16c514905f5f367dbcd0ea9ecb1cb006\\transformed\\material3-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,407,525,621,715,826,970,1091,1233,1318,1416,1511,1610,1726,1854,1957,2088,2218,2347,2527,2647,2765,2889,3022,3118,3214,3335,3461,3558,3668,3776,3912,4056,4166,4268,4345,4446,4547,4638,4730,4839,4919,5004,5105,5210,5308,5410,5497,5604,5703,5807,5928,6008,6111", "endColumns": "118,119,112,117,95,93,110,143,120,141,84,97,94,98,115,127,102,130,129,128,179,119,117,123,132,95,95,120,125,96,109,107,135,143,109,101,76,100,100,90,91,108,79,84,100,104,97,101,86,106,98,103,120,79,102,93", "endOffsets": "169,289,402,520,616,710,821,965,1086,1228,1313,1411,1506,1605,1721,1849,1952,2083,2213,2342,2522,2642,2760,2884,3017,3113,3209,3330,3456,3553,3663,3771,3907,4051,4161,4263,4340,4441,4542,4633,4725,4834,4914,4999,5100,5205,5303,5405,5492,5599,5698,5802,5923,6003,6106,6200"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1509,1628,1748,1861,1979,2075,2169,2280,2424,2545,2687,2772,2870,2965,3064,3180,3308,3411,3542,3672,3801,3981,4101,4219,4343,4476,4572,4668,4789,4915,5012,5122,5230,5366,5510,5620,5722,5799,5900,6001,6092,6184,6293,6373,6458,6559,6664,6762,6864,6951,7058,7157,7261,7382,7462,7565", "endColumns": "118,119,112,117,95,93,110,143,120,141,84,97,94,98,115,127,102,130,129,128,179,119,117,123,132,95,95,120,125,96,109,107,135,143,109,101,76,100,100,90,91,108,79,84,100,104,97,101,86,106,98,103,120,79,102,93", "endOffsets": "1623,1743,1856,1974,2070,2164,2275,2419,2540,2682,2767,2865,2960,3059,3175,3303,3406,3537,3667,3796,3976,4096,4214,4338,4471,4567,4663,4784,4910,5007,5117,5225,5361,5505,5615,5717,5794,5895,5996,6087,6179,6288,6368,6453,6554,6659,6757,6859,6946,7053,7152,7256,7377,7457,7560,7654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a8c37811fe2277121ba6925381f444a4\\transformed\\foundation-release\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8585,8670", "endColumns": "84,87", "endOffsets": "8665,8753"}}]}]}