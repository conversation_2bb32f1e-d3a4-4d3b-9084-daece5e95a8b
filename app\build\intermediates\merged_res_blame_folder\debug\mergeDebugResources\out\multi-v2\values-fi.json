{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-2:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6c315b7243f9366af29f4dfe1c8f7ae0\\transformed\\foundation-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8539,8629", "endColumns": "89,89", "endOffsets": "8624,8714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f09be75b05f28281f1ac42504082277d\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,303,401,506,611,723,8167", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "196,298,396,501,606,718,834,8263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\eacb11c3dc0856eb37fac4a2b512c744\\transformed\\ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,982,1050,1131,1213,1285,1362,1434", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,977,1045,1126,1208,1280,1357,1429,1551"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "839,933,1018,1117,1218,1307,1384,7625,7716,7798,7864,7932,8013,8095,8268,8345,8417", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "928,1013,1112,1213,1302,1379,1472,7711,7793,7859,7927,8008,8090,8162,8340,8412,8534"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7dcdec81c6dab61c12a6af226d4a0a33\\transformed\\material3-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,393,507,604,705,823,960,1082,1234,1324,1420,1518,1620,1738,1861,1962,2094,2226,2355,2522,2644,2768,2895,3017,3116,3215,3336,3457,3560,3671,3779,3918,4062,4170,4276,4359,4457,4554,4638,4723,4823,4903,4988,5085,5188,5285,5390,5480,5588,5691,5801,5919,5999,6104", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "166,279,388,502,599,700,818,955,1077,1229,1319,1415,1513,1615,1733,1856,1957,2089,2221,2350,2517,2639,2763,2890,3012,3111,3210,3331,3452,3555,3666,3774,3913,4057,4165,4271,4354,4452,4549,4633,4718,4818,4898,4983,5080,5183,5280,5385,5475,5583,5686,5796,5914,5994,6099,6198"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1477,1593,1706,1815,1929,2026,2127,2245,2382,2504,2656,2746,2842,2940,3042,3160,3283,3384,3516,3648,3777,3944,4066,4190,4317,4439,4538,4637,4758,4879,4982,5093,5201,5340,5484,5592,5698,5781,5879,5976,6060,6145,6245,6325,6410,6507,6610,6707,6812,6902,7010,7113,7223,7341,7421,7526", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "1588,1701,1810,1924,2021,2122,2240,2377,2499,2651,2741,2837,2935,3037,3155,3278,3379,3511,3643,3772,3939,4061,4185,4312,4434,4533,4632,4753,4874,4977,5088,5196,5335,5479,5587,5693,5776,5874,5971,6055,6140,6240,6320,6405,6502,6605,6702,6807,6897,7005,7108,7218,7336,7416,7521,7620"}}]}]}