  Activity android.app  AA103_POCTheme android.app.Activity  Bundle android.app.Activity  
MainScreen android.app.Activity  Modifier android.app.Activity  Scaffold android.app.Activity  SignInScreen android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  
setContent android.app.Activity  	viewModel android.app.Activity  Context android.content  Intent android.content  AA103_POCTheme android.content.Context  Bundle android.content.Context  
MainScreen android.content.Context  Modifier android.content.Context  Scaffold android.content.Context  SignInScreen android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  onCreate android.content.Context  padding android.content.Context  
setContent android.content.Context  
startActivity android.content.Context  	viewModel android.content.Context  AA103_POCTheme android.content.ContextWrapper  Bundle android.content.ContextWrapper  
MainScreen android.content.ContextWrapper  Modifier android.content.ContextWrapper  Scaffold android.content.ContextWrapper  SignInScreen android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  onCreate android.content.ContextWrapper  padding android.content.ContextWrapper  
setContent android.content.ContextWrapper  	viewModel android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  Patterns android.util  
EMAIL_ADDRESS android.util.Patterns  AA103_POCTheme  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  
MainScreen  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  SignInScreen  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  	viewModel  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  AA103_POCTheme #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  SignInScreen #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  	viewModel #androidx.activity.ComponentActivity  AA103_POCTheme -androidx.activity.ComponentActivity.Companion  
MainScreen -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  Scaffold -androidx.activity.ComponentActivity.Companion  SignInScreen -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  getFILLMaxSize -androidx.activity.ComponentActivity.Companion  getFillMaxSize -androidx.activity.ComponentActivity.Companion  
getPADDING -androidx.activity.ComponentActivity.Companion  
getPadding -androidx.activity.ComponentActivity.Companion  getVIEWModel -androidx.activity.ComponentActivity.Companion  getViewModel -androidx.activity.ComponentActivity.Companion  padding -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  	viewModel -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Button .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Intent .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  SignInActivity .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  
Visibility .androidx.compose.foundation.layout.ColumnScope  
VisibilityOff .androidx.compose.foundation.layout.ColumnScope  VisualTransformation .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  invoke .androidx.compose.foundation.layout.ColumnScope  java .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  
getPADDING +androidx.compose.foundation.layout.RowScope  
getPadding +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  KeyboardOptions  androidx.compose.foundation.text  invoke :androidx.compose.foundation.text.KeyboardOptions.Companion  Icons androidx.compose.material.icons  Filled %androidx.compose.material.icons.Icons  
Visibility ,androidx.compose.material.icons.Icons.Filled  
VisibilityOff ,androidx.compose.material.icons.Icons.Filled  
Visibility &androidx.compose.material.icons.filled  
VisibilityOff &androidx.compose.material.icons.filled  Button androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  
MaterialTheme androidx.compose.material3  OutlinedTextField androidx.compose.material3  Scaffold androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  error &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
Composable androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  State androidx.compose.runtime  collectAsState androidx.compose.runtime  getValue androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  CenterHorizontally androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  getFILLMaxSize androidx.compose.ui.Modifier  getFillMaxSize androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  VisualTransformation androidx.compose.ui.text.input  Email +androidx.compose.ui.text.input.KeyboardType  Password +androidx.compose.ui.text.input.KeyboardType  Email 5androidx.compose.ui.text.input.KeyboardType.Companion  Password 5androidx.compose.ui.text.input.KeyboardType.Companion  None 3androidx.compose.ui.text.input.VisualTransformation  None =androidx.compose.ui.text.input.VisualTransformation.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AA103_POCTheme #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  SignInScreen #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  	viewModel #androidx.core.app.ComponentActivity  	ViewModel androidx.lifecycle  Boolean androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  
SignInUiState androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  android androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  
clearError androidx.lifecycle.ViewModel  isBlank androidx.lifecycle.ViewModel  isValidEmail androidx.lifecycle.ViewModel  signIn androidx.lifecycle.ViewModel  togglePasswordVisibility androidx.lifecycle.ViewModel  updateEmail androidx.lifecycle.ViewModel  updatePassword androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  AA103_POCTheme com.dev.aa103_poc  Button com.dev.aa103_poc  Intent com.dev.aa103_poc  MainActivity com.dev.aa103_poc  
MainScreen com.dev.aa103_poc  MainScreenPreview com.dev.aa103_poc  Modifier com.dev.aa103_poc  Scaffold com.dev.aa103_poc  SignInActivity com.dev.aa103_poc  Text com.dev.aa103_poc  enableEdgeToEdge com.dev.aa103_poc  fillMaxSize com.dev.aa103_poc  java com.dev.aa103_poc  padding com.dev.aa103_poc  
setContent com.dev.aa103_poc  AA103_POCTheme com.dev.aa103_poc.MainActivity  Bundle com.dev.aa103_poc.MainActivity  
MainScreen com.dev.aa103_poc.MainActivity  Modifier com.dev.aa103_poc.MainActivity  Scaffold com.dev.aa103_poc.MainActivity  enableEdgeToEdge com.dev.aa103_poc.MainActivity  fillMaxSize com.dev.aa103_poc.MainActivity  getENABLEEdgeToEdge com.dev.aa103_poc.MainActivity  getEnableEdgeToEdge com.dev.aa103_poc.MainActivity  getFILLMaxSize com.dev.aa103_poc.MainActivity  getFillMaxSize com.dev.aa103_poc.MainActivity  
getPADDING com.dev.aa103_poc.MainActivity  
getPadding com.dev.aa103_poc.MainActivity  
getSETContent com.dev.aa103_poc.MainActivity  
getSetContent com.dev.aa103_poc.MainActivity  padding com.dev.aa103_poc.MainActivity  
setContent com.dev.aa103_poc.MainActivity  AA103_POCTheme com.dev.aa103_poc.ui.signin  Boolean com.dev.aa103_poc.ui.signin  Button com.dev.aa103_poc.ui.signin  CircularProgressIndicator com.dev.aa103_poc.ui.signin  Icon com.dev.aa103_poc.ui.signin  
IconButton com.dev.aa103_poc.ui.signin  Icons com.dev.aa103_poc.ui.signin  KeyboardOptions com.dev.aa103_poc.ui.signin  KeyboardType com.dev.aa103_poc.ui.signin  
MaterialTheme com.dev.aa103_poc.ui.signin  Modifier com.dev.aa103_poc.ui.signin  MutableStateFlow com.dev.aa103_poc.ui.signin  OutlinedTextField com.dev.aa103_poc.ui.signin  PasswordVisualTransformation com.dev.aa103_poc.ui.signin  Scaffold com.dev.aa103_poc.ui.signin  SignInActivity com.dev.aa103_poc.ui.signin  
SignInContent com.dev.aa103_poc.ui.signin  SignInScreen com.dev.aa103_poc.ui.signin  SignInScreenPreview com.dev.aa103_poc.ui.signin  
SignInUiState com.dev.aa103_poc.ui.signin  SignInViewModel com.dev.aa103_poc.ui.signin  Spacer com.dev.aa103_poc.ui.signin  String com.dev.aa103_poc.ui.signin  Text com.dev.aa103_poc.ui.signin  Unit com.dev.aa103_poc.ui.signin  VisualTransformation com.dev.aa103_poc.ui.signin  android com.dev.aa103_poc.ui.signin  asStateFlow com.dev.aa103_poc.ui.signin  enableEdgeToEdge com.dev.aa103_poc.ui.signin  fillMaxSize com.dev.aa103_poc.ui.signin  fillMaxWidth com.dev.aa103_poc.ui.signin  height com.dev.aa103_poc.ui.signin  isBlank com.dev.aa103_poc.ui.signin  padding com.dev.aa103_poc.ui.signin  provideDelegate com.dev.aa103_poc.ui.signin  
setContent com.dev.aa103_poc.ui.signin  	viewModel com.dev.aa103_poc.ui.signin  AA103_POCTheme *com.dev.aa103_poc.ui.signin.SignInActivity  Bundle *com.dev.aa103_poc.ui.signin.SignInActivity  Modifier *com.dev.aa103_poc.ui.signin.SignInActivity  Scaffold *com.dev.aa103_poc.ui.signin.SignInActivity  SignInScreen *com.dev.aa103_poc.ui.signin.SignInActivity  enableEdgeToEdge *com.dev.aa103_poc.ui.signin.SignInActivity  fillMaxSize *com.dev.aa103_poc.ui.signin.SignInActivity  getENABLEEdgeToEdge *com.dev.aa103_poc.ui.signin.SignInActivity  getEnableEdgeToEdge *com.dev.aa103_poc.ui.signin.SignInActivity  getFILLMaxSize *com.dev.aa103_poc.ui.signin.SignInActivity  getFillMaxSize *com.dev.aa103_poc.ui.signin.SignInActivity  
getPADDING *com.dev.aa103_poc.ui.signin.SignInActivity  
getPadding *com.dev.aa103_poc.ui.signin.SignInActivity  
getSETContent *com.dev.aa103_poc.ui.signin.SignInActivity  
getSetContent *com.dev.aa103_poc.ui.signin.SignInActivity  getVIEWModel *com.dev.aa103_poc.ui.signin.SignInActivity  getViewModel *com.dev.aa103_poc.ui.signin.SignInActivity  padding *com.dev.aa103_poc.ui.signin.SignInActivity  
setContent *com.dev.aa103_poc.ui.signin.SignInActivity  	viewModel *com.dev.aa103_poc.ui.signin.SignInActivity  Boolean )com.dev.aa103_poc.ui.signin.SignInUiState  String )com.dev.aa103_poc.ui.signin.SignInUiState  copy )com.dev.aa103_poc.ui.signin.SignInUiState  email )com.dev.aa103_poc.ui.signin.SignInUiState  errorMessage )com.dev.aa103_poc.ui.signin.SignInUiState  	isLoading )com.dev.aa103_poc.ui.signin.SignInUiState  isPasswordVisible )com.dev.aa103_poc.ui.signin.SignInUiState  password )com.dev.aa103_poc.ui.signin.SignInUiState  Boolean +com.dev.aa103_poc.ui.signin.SignInViewModel  MutableStateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  
SignInUiState +com.dev.aa103_poc.ui.signin.SignInViewModel  	StateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  String +com.dev.aa103_poc.ui.signin.SignInViewModel  _uiState +com.dev.aa103_poc.ui.signin.SignInViewModel  android +com.dev.aa103_poc.ui.signin.SignInViewModel  asStateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  
clearError +com.dev.aa103_poc.ui.signin.SignInViewModel  
getANDROID +com.dev.aa103_poc.ui.signin.SignInViewModel  getASStateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  
getAndroid +com.dev.aa103_poc.ui.signin.SignInViewModel  getAsStateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  
getISBlank +com.dev.aa103_poc.ui.signin.SignInViewModel  
getIsBlank +com.dev.aa103_poc.ui.signin.SignInViewModel  isBlank +com.dev.aa103_poc.ui.signin.SignInViewModel  isValidEmail +com.dev.aa103_poc.ui.signin.SignInViewModel  signIn +com.dev.aa103_poc.ui.signin.SignInViewModel  togglePasswordVisibility +com.dev.aa103_poc.ui.signin.SignInViewModel  uiState +com.dev.aa103_poc.ui.signin.SignInViewModel  updateEmail +com.dev.aa103_poc.ui.signin.SignInViewModel  updatePassword +com.dev.aa103_poc.ui.signin.SignInViewModel  AA103_POCTheme com.dev.aa103_poc.ui.theme  Boolean com.dev.aa103_poc.ui.theme  Build com.dev.aa103_poc.ui.theme  DarkColorScheme com.dev.aa103_poc.ui.theme  LightColorScheme com.dev.aa103_poc.ui.theme  Pink40 com.dev.aa103_poc.ui.theme  Pink80 com.dev.aa103_poc.ui.theme  Purple40 com.dev.aa103_poc.ui.theme  Purple80 com.dev.aa103_poc.ui.theme  PurpleGrey40 com.dev.aa103_poc.ui.theme  PurpleGrey80 com.dev.aa103_poc.ui.theme  
Typography com.dev.aa103_poc.ui.theme  Unit com.dev.aa103_poc.ui.theme  AA103_POCTheme 	java.lang  Build 	java.lang  Button 	java.lang  CircularProgressIndicator 	java.lang  Class 	java.lang  Icon 	java.lang  
IconButton 	java.lang  Icons 	java.lang  Intent 	java.lang  KeyboardOptions 	java.lang  KeyboardType 	java.lang  
MainScreen 	java.lang  
MaterialTheme 	java.lang  Modifier 	java.lang  MutableStateFlow 	java.lang  OutlinedTextField 	java.lang  PasswordVisualTransformation 	java.lang  Scaffold 	java.lang  SignInActivity 	java.lang  SignInScreen 	java.lang  
SignInUiState 	java.lang  Spacer 	java.lang  Text 	java.lang  VisualTransformation 	java.lang  android 	java.lang  asStateFlow 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  height 	java.lang  isBlank 	java.lang  java 	java.lang  padding 	java.lang  provideDelegate 	java.lang  	viewModel 	java.lang  matches java.util.regex.Matcher  matcher java.util.regex.Pattern  AA103_POCTheme kotlin  Boolean kotlin  Build kotlin  Button kotlin  CircularProgressIndicator kotlin  Double kotlin  	Function0 kotlin  	Function1 kotlin  Icon kotlin  
IconButton kotlin  Icons kotlin  Int kotlin  Intent kotlin  KeyboardOptions kotlin  KeyboardType kotlin  
MainScreen kotlin  
MaterialTheme kotlin  Modifier kotlin  MutableStateFlow kotlin  Nothing kotlin  OutlinedTextField kotlin  PasswordVisualTransformation kotlin  Scaffold kotlin  SignInActivity kotlin  SignInScreen kotlin  
SignInUiState kotlin  Spacer kotlin  String kotlin  Text kotlin  Unit kotlin  VisualTransformation kotlin  android kotlin  asStateFlow kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  height kotlin  isBlank kotlin  java kotlin  padding kotlin  provideDelegate kotlin  	viewModel kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  
getISBlank 
kotlin.String  
getIsBlank 
kotlin.String  isBlank 
kotlin.String  AA103_POCTheme kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  CircularProgressIndicator kotlin.annotation  Icon kotlin.annotation  
IconButton kotlin.annotation  Icons kotlin.annotation  Intent kotlin.annotation  KeyboardOptions kotlin.annotation  KeyboardType kotlin.annotation  
MainScreen kotlin.annotation  
MaterialTheme kotlin.annotation  Modifier kotlin.annotation  MutableStateFlow kotlin.annotation  OutlinedTextField kotlin.annotation  PasswordVisualTransformation kotlin.annotation  Scaffold kotlin.annotation  SignInActivity kotlin.annotation  SignInScreen kotlin.annotation  
SignInUiState kotlin.annotation  Spacer kotlin.annotation  Text kotlin.annotation  VisualTransformation kotlin.annotation  android kotlin.annotation  asStateFlow kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  height kotlin.annotation  isBlank kotlin.annotation  java kotlin.annotation  padding kotlin.annotation  provideDelegate kotlin.annotation  	viewModel kotlin.annotation  AA103_POCTheme kotlin.collections  Build kotlin.collections  Button kotlin.collections  CircularProgressIndicator kotlin.collections  Icon kotlin.collections  
IconButton kotlin.collections  Icons kotlin.collections  Intent kotlin.collections  KeyboardOptions kotlin.collections  KeyboardType kotlin.collections  
MainScreen kotlin.collections  
MaterialTheme kotlin.collections  Modifier kotlin.collections  MutableStateFlow kotlin.collections  OutlinedTextField kotlin.collections  PasswordVisualTransformation kotlin.collections  Scaffold kotlin.collections  SignInActivity kotlin.collections  SignInScreen kotlin.collections  
SignInUiState kotlin.collections  Spacer kotlin.collections  Text kotlin.collections  VisualTransformation kotlin.collections  android kotlin.collections  asStateFlow kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  height kotlin.collections  isBlank kotlin.collections  java kotlin.collections  padding kotlin.collections  provideDelegate kotlin.collections  	viewModel kotlin.collections  AA103_POCTheme kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  CircularProgressIndicator kotlin.comparisons  Icon kotlin.comparisons  
IconButton kotlin.comparisons  Icons kotlin.comparisons  Intent kotlin.comparisons  KeyboardOptions kotlin.comparisons  KeyboardType kotlin.comparisons  
MainScreen kotlin.comparisons  
MaterialTheme kotlin.comparisons  Modifier kotlin.comparisons  MutableStateFlow kotlin.comparisons  OutlinedTextField kotlin.comparisons  PasswordVisualTransformation kotlin.comparisons  Scaffold kotlin.comparisons  SignInActivity kotlin.comparisons  SignInScreen kotlin.comparisons  
SignInUiState kotlin.comparisons  Spacer kotlin.comparisons  Text kotlin.comparisons  VisualTransformation kotlin.comparisons  android kotlin.comparisons  asStateFlow kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  height kotlin.comparisons  isBlank kotlin.comparisons  java kotlin.comparisons  padding kotlin.comparisons  provideDelegate kotlin.comparisons  	viewModel kotlin.comparisons  AA103_POCTheme 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  CircularProgressIndicator 	kotlin.io  Icon 	kotlin.io  
IconButton 	kotlin.io  Icons 	kotlin.io  Intent 	kotlin.io  KeyboardOptions 	kotlin.io  KeyboardType 	kotlin.io  
MainScreen 	kotlin.io  
MaterialTheme 	kotlin.io  Modifier 	kotlin.io  MutableStateFlow 	kotlin.io  OutlinedTextField 	kotlin.io  PasswordVisualTransformation 	kotlin.io  Scaffold 	kotlin.io  SignInActivity 	kotlin.io  SignInScreen 	kotlin.io  
SignInUiState 	kotlin.io  Spacer 	kotlin.io  Text 	kotlin.io  VisualTransformation 	kotlin.io  android 	kotlin.io  asStateFlow 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  height 	kotlin.io  isBlank 	kotlin.io  java 	kotlin.io  padding 	kotlin.io  provideDelegate 	kotlin.io  	viewModel 	kotlin.io  AA103_POCTheme 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  CircularProgressIndicator 
kotlin.jvm  Icon 
kotlin.jvm  
IconButton 
kotlin.jvm  Icons 
kotlin.jvm  Intent 
kotlin.jvm  KeyboardOptions 
kotlin.jvm  KeyboardType 
kotlin.jvm  
MainScreen 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  Modifier 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OutlinedTextField 
kotlin.jvm  PasswordVisualTransformation 
kotlin.jvm  Scaffold 
kotlin.jvm  SignInActivity 
kotlin.jvm  SignInScreen 
kotlin.jvm  
SignInUiState 
kotlin.jvm  Spacer 
kotlin.jvm  Text 
kotlin.jvm  VisualTransformation 
kotlin.jvm  android 
kotlin.jvm  asStateFlow 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  height 
kotlin.jvm  isBlank 
kotlin.jvm  java 
kotlin.jvm  padding 
kotlin.jvm  provideDelegate 
kotlin.jvm  	viewModel 
kotlin.jvm  AA103_POCTheme 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  CircularProgressIndicator 
kotlin.ranges  Icon 
kotlin.ranges  
IconButton 
kotlin.ranges  Icons 
kotlin.ranges  Intent 
kotlin.ranges  KeyboardOptions 
kotlin.ranges  KeyboardType 
kotlin.ranges  
MainScreen 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  Modifier 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OutlinedTextField 
kotlin.ranges  PasswordVisualTransformation 
kotlin.ranges  Scaffold 
kotlin.ranges  SignInActivity 
kotlin.ranges  SignInScreen 
kotlin.ranges  
SignInUiState 
kotlin.ranges  Spacer 
kotlin.ranges  Text 
kotlin.ranges  VisualTransformation 
kotlin.ranges  android 
kotlin.ranges  asStateFlow 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  height 
kotlin.ranges  isBlank 
kotlin.ranges  java 
kotlin.ranges  padding 
kotlin.ranges  provideDelegate 
kotlin.ranges  	viewModel 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  AA103_POCTheme kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  CircularProgressIndicator kotlin.sequences  Icon kotlin.sequences  
IconButton kotlin.sequences  Icons kotlin.sequences  Intent kotlin.sequences  KeyboardOptions kotlin.sequences  KeyboardType kotlin.sequences  
MainScreen kotlin.sequences  
MaterialTheme kotlin.sequences  Modifier kotlin.sequences  MutableStateFlow kotlin.sequences  OutlinedTextField kotlin.sequences  PasswordVisualTransformation kotlin.sequences  Scaffold kotlin.sequences  SignInActivity kotlin.sequences  SignInScreen kotlin.sequences  
SignInUiState kotlin.sequences  Spacer kotlin.sequences  Text kotlin.sequences  VisualTransformation kotlin.sequences  android kotlin.sequences  asStateFlow kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  height kotlin.sequences  isBlank kotlin.sequences  java kotlin.sequences  padding kotlin.sequences  provideDelegate kotlin.sequences  	viewModel kotlin.sequences  AA103_POCTheme kotlin.text  Build kotlin.text  Button kotlin.text  CircularProgressIndicator kotlin.text  Icon kotlin.text  
IconButton kotlin.text  Icons kotlin.text  Intent kotlin.text  KeyboardOptions kotlin.text  KeyboardType kotlin.text  
MainScreen kotlin.text  
MaterialTheme kotlin.text  Modifier kotlin.text  MutableStateFlow kotlin.text  OutlinedTextField kotlin.text  PasswordVisualTransformation kotlin.text  Scaffold kotlin.text  SignInActivity kotlin.text  SignInScreen kotlin.text  
SignInUiState kotlin.text  Spacer kotlin.text  Text kotlin.text  VisualTransformation kotlin.text  android kotlin.text  asStateFlow kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  height kotlin.text  isBlank kotlin.text  java kotlin.text  padding kotlin.text  provideDelegate kotlin.text  	viewModel kotlin.text  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  getCOLLECTAsState !kotlinx.coroutines.flow.StateFlow  getCollectAsState !kotlinx.coroutines.flow.StateFlow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                