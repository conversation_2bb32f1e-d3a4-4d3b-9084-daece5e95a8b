{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-2:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\eacb11c3dc0856eb37fac4a2b512c744\\transformed\\ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,974,1039,1120,1203,1280,1364,1434", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,969,1034,1115,1198,1275,1359,1429,1549"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "839,931,1014,1111,1210,1295,1371,7642,7729,7818,7883,7948,8029,8112,8290,8374,8444", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "926,1009,1106,1205,1290,1366,1462,7724,7813,7878,7943,8024,8107,8184,8369,8439,8559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6c315b7243f9366af29f4dfe1c8f7ae0\\transformed\\foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,85", "endOffsets": "136,222"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8564,8650", "endColumns": "85,85", "endOffsets": "8645,8731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7dcdec81c6dab61c12a6af226d4a0a33\\transformed\\material3-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,415,533,633,731,846,998,1119,1261,1346,1445,1541,1644,1762,1883,1987,2118,2246,2382,2560,2691,2811,2932,3067,3164,3264,3384,3513,3613,3720,3823,3960,4100,4206,4310,4394,4494,4591,4678,4765,4870,4950,5033,5132,5236,5331,5430,5518,5628,5729,5834,5954,6034,6135", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "172,289,410,528,628,726,841,993,1114,1256,1341,1440,1536,1639,1757,1878,1982,2113,2241,2377,2555,2686,2806,2927,3062,3159,3259,3379,3508,3608,3715,3818,3955,4095,4201,4305,4389,4489,4586,4673,4760,4865,4945,5028,5127,5231,5326,5425,5513,5623,5724,5829,5949,6029,6130,6225"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1467,1589,1706,1827,1945,2045,2143,2258,2410,2531,2673,2758,2857,2953,3056,3174,3295,3399,3530,3658,3794,3972,4103,4223,4344,4479,4576,4676,4796,4925,5025,5132,5235,5372,5512,5618,5722,5806,5906,6003,6090,6177,6282,6362,6445,6544,6648,6743,6842,6930,7040,7141,7246,7366,7446,7547", "endColumns": "121,116,120,117,99,97,114,151,120,141,84,98,95,102,117,120,103,130,127,135,177,130,119,120,134,96,99,119,128,99,106,102,136,139,105,103,83,99,96,86,86,104,79,82,98,103,94,98,87,109,100,104,119,79,100,94", "endOffsets": "1584,1701,1822,1940,2040,2138,2253,2405,2526,2668,2753,2852,2948,3051,3169,3290,3394,3525,3653,3789,3967,4098,4218,4339,4474,4571,4671,4791,4920,5020,5127,5230,5367,5507,5613,5717,5801,5901,5998,6085,6172,6277,6357,6440,6539,6643,6738,6837,6925,7035,7136,7241,7361,7441,7542,7637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f09be75b05f28281f1ac42504082277d\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,309,409,509,616,720,8189", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "202,304,404,504,611,715,834,8285"}}]}]}